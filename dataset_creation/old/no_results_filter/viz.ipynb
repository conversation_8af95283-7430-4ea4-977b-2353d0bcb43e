{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Perplexity and Spike Score Distribution Analysis\n", "\n", "This notebook visualizes the distribution of perplexity (PPL) scores from a dataset to help understand model behavior under different conditions (baseline vs. pressure) and analyze the resulting 'Spike Scores'."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Data Loading\n", "\n", "First, we import the necessary libraries and load the pre-computed PPL data from `ppl_results.jsonl`. We then process this data to extract the relevant metrics for plotting."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully loaded and processed 2000 records.\n"]}], "source": ["import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import math\n", "\n", "# Define the spike score calculation function\n", "def _calculate_spike_score(max_ppl_baseline, max_ppl_pressure):\n", "    \"\"\"Helper to calculate the spike score.\"\"\"\n", "    if max_ppl_baseline > 0 and max_ppl_pressure > max_ppl_baseline:\n", "        absolute_diff = max_ppl_pressure - max_ppl_baseline\n", "        ratio = max_ppl_pressure / max_ppl_baseline\n", "        return absolute_diff * math.log(ratio + 1)\n", "    return 0.0\n", "\n", "# Load the data from the JSONL file\n", "def load_data(file_path):\n", "    data = []\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            data.append(json.loads(line))\n", "    return data\n", "\n", "# --- Data Extraction ---\n", "file_path = 'ppl_results.jsonl'\n", "try:\n", "    ppl_data = load_data(file_path)\n", "\n", "    # Extract PPLs and calculate spike scores\n", "    baseline_avg_ppls = [item['baseline_result']['avg_ppl'] for item in ppl_data]\n", "    pressure_avg_ppls = [item['pressure_result']['avg_ppl'] for item in ppl_data]\n", "    spike_scores = [\n", "        _calculate_spike_score(\n", "            item['baseline_result']['max_ppl'],\n", "            item['pressure_result']['max_ppl']\n", "        ) for item in ppl_data\n", "    ]\n", "    # Filter out zero scores for a more meaningful distribution plot\n", "    spike_scores = [s for s in spike_scores if s > 0]\n", "\n", "    print(f\"Successfully loaded and processed {len(ppl_data)} records.\")\n", "except FileNotFoundError:\n", "    print(f\"Error: The file '{file_path}' was not found. Please run the `generate_ppl` command first.\")\n", "    baseline_avg_ppls, pressure_avg_ppls, spike_scores = [], [], []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Perplexity Distribution Analysis\n", "\n", "We create two histograms to compare the distribution of average perplexity scores between the baseline (neutral prompt) and pressure scenarios."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABjUAAAJRCAYAAAANqTVWAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAA3bRJREFUeJzs3XdYFMf/B/A3XYp01IhiB0VQEBVREEXs2I29BpVobDFqTGLUqLF3SexRsKJGoyJqYu9ir9hRRCw0QUTh4O73B7/bL8sd1UPu9P16njzJzs7tzs7ckdn97MxoyWQyGYiIiIiIiIiIiIiIiNScdkkXgIiIiIiIiIiIiIiIqCAY1CAiIiIiIiIiIiIiIo3AoAYREREREREREREREWkEBjWIiIiIiIiIiIiIiEgjMKhBREREREREREREREQagUENIiIiIiIiIiIiIiLSCAxqEBERERERERERERGRRmBQg4iIiIiIiIiIiIiINAKDGkREREREREREREREpBEY1CAiIiIiKoJJkybBwcFB+Kd///4lXSSlfHx8ROVcvnx5SRepUNLT00XX0Ldv35IuUrHbuXOnqM1Onz5d0kVS0L9/f1EZJ02aVNJFKpTsZXdwcMCuXbtE+3ft2qWQR5Noyt8nIiIioqLQLekCEBER0efn8OHD+O677xTSW7dujWXLlpVAiUjV8nvAV6pUKVhYWMDe3h7NmjVDx44dYWJi8olKR4W1YcMGvH37Vthu2LAh3N3dS7BE/xMcHIznz58L28OHD1fI079/f4SHh+d6DD09PZiamqJKlSpo1KgRunbtCltbW4V8y5cvR2BgYK7H0dHRgbGxMSpUqABXV1d07twZderUUch34cIFDBgwQJQ2cuRIjBo1KtdjZ9epUyf88ccfiImJAQDMmzcPjRs3hrZ20d5JU1YeANDV1YWenh6MjY1haWkJW1tb1K5dG+3atUO1atWKdC5VyRl88/X1Ra1atUqoNCUvOjoau3fvFqUNHDgQpqamJVQiIiIiopLDoAYRERGpXM43XuWOHj2KN2/ewNzc/NMWiD65Dx8+4MWLF3jx4gVOnDiBFStWYNGiRWjQoEFJF42UyBk4GDlypFoENRITE7Fq1Sph29nZGZ6enoU+jkQiQXx8POLj43Hp0iWsWbMGEyZMKPTb65mZmUhOTsadO3dw584dbN68GV27dsX06dOhp6dX6HLlRk9PD0OGDMH06dMBAPfu3cOuXbvQvXt3lZ0DADIyMpCRkYH3798jLi4O9+/fx7FjxxAYGIjGjRvjt99+g52dnUrPWVA5g0u2trZfdFDj+fPnCnXSpUsXBjWIiIjoi8SgBhEREalUQkICTp48qXSfRCJBaGgo+vXr94lLRSXt9evXGDp0KPbs2YNKlSqVdHG+KFu2bEFGRoawrUkPQUNCQpCcnCxs9+jRQyXHTUtLw8yZM2Fubo4OHTp81LF27doFXV1dzJgxQyVlk+vUqRPmzp2LtLQ0AMDatWtVHtTIy9mzZ9GlSxcsWrQI3t7eCvsXLVoklA0AjIyMPlnZVOHIkSOibQsLixIqSfGYOHEiRo4cKWwbGBiUYGmIiIiIVItBDSIiIlKpvXv3QiKR5Lp/**************************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", "text/plain": ["<Figure size 1600x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use('seaborn-v0_8-whitegrid')\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6), sharey=True)\n", "fig.suptitle('Average Perplexity (PPL) Distribution', fontsize=18, fontweight='bold')\n", "\n", "# --- Plot 1: Baseline PPL Distribution ---\n", "if baseline_avg_ppls:\n", "    # Calculate stats\n", "    mean_bl = np.mean(baseline_avg_ppls)\n", "    median_bl = np.median(baseline_avg_ppls)\n", "    std_bl = np.std(baseline_avg_ppls)\n", "\n", "    # Define bins\n", "    max_val = math.ceil(max(baseline_avg_ppls))\n", "    bins_bl = np.arange(0, max_val + 2, 1.0)\n", "\n", "    # Plot histogram\n", "    axes[0].hist(baseline_avg_ppls, bins=bins_bl, color='#4c72b0', alpha=0.8, edgecolor='black')\n", "    axes[0].set_title('Baseline Scenario Responses', fontsize=14)\n", "    axes[0].set_xlabel('Average Perplexity (PPL)', fontsize=12)\n", "    axes[0].set_ylabel('Frequency (Count)', fontsize=12)\n", "    axes[0].set_xlim(0, max(20, max_val)) # Set a reasonable x-limit\n", "\n", "    # Add stats text\n", "    stats_text = f'Mean: {mean_bl:.2f}\\nMedian: {median_bl:.2f}\\nStd Dev: {std_bl:.2f}'\n", "    axes[0].text(0.95, 0.95, stats_text, transform=axes[0].transAxes, fontsize=11,\n", "                 verticalalignment='top', horizontalalignment='right', bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.6))\n", "\n", "# --- Plot 2: Pressure PPL Distribution ---\n", "if pressure_avg_ppls:\n", "    # Calculate stats\n", "    mean_pr = np.mean(pressure_avg_ppls)\n", "    median_pr = np.median(pressure_avg_ppls)\n", "    std_pr = np.std(pressure_avg_ppls)\n", "\n", "    # Define bins\n", "    max_val = math.ceil(max(pressure_avg_ppls))\n", "    bins_pr = np.arange(0, max_val + 2, 1.0)\n", "\n", "    # Plot histogram\n", "    axes[1].hist(pressure_avg_ppls, bins=bins_pr, color='#dd8452', alpha=0.8, edgecolor='black')\n", "    axes[1].set_title('Pressure Scenario Responses', fontsize=14)\n", "    axes[1].set_xlabel('Average Perplexity (PPL)', fontsize=12)\n", "    axes[1].set_xlim(0, max(20, max_val))\n", "\n", "    # Add stats text\n", "    stats_text = f'Mean: {mean_pr:.2f}\\nMedian: {median_pr:.2f}\\nStd Dev: {std_pr:.2f}'\n", "    axes[1].text(0.95, 0.95, stats_text, transform=axes[1].transAxes, fontsize=11,\n", "                 verticalalignment='top', horizontalalignment='right', bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.6))\n", "\n", "plt.tight_layout(rect=[0, 0, 1, 0.96])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Spike Score Distribution Analysis\n", "\n", "This histogram shows the distribution of the calculated `spike_score`. Since the scores can be heavily skewed (a few very large values), we use a logarithmic scale for the x-axis to better visualize the distribution across different orders of magnitude."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.colors as mcolors\n", "from matplotlib.ticker import ScalarFormatter # Import the formatter\n", "\n", "if spike_scores:\n", "    plt.figure(figsize=(12, 7))\n", "\n", "    # Calculate stats\n", "    mean_s = np.mean(spike_scores)\n", "    median_s = np.median(spike_scores)\n", "    std_s = np.std(spike_scores)\n", "\n", "    # Create logarithmically spaced bins for the histogram\n", "    min_val = np.log10(min(spike_scores))\n", "    max_val = np.log10(max(spike_scores))\n", "    log_bins = np.logspace(min_val, max_val, 50)\n", "\n", "    # Create histogram with the new log bins\n", "    plt.hist(spike_scores, bins=log_bins, color='#55a868', alpha=0.8, edgecolor='black')\n", "    plt.gca().set_xscale('log')\n", "\n", "    # --- FIX: Format the x-axis to show regular numbers ---\n", "    ax = plt.gca()\n", "    formatter = ScalarFormatter()\n", "    formatter.set_scientific(False)\n", "    ax.xaxis.set_major_formatter(formatter)\n", "\n", "    # Titles and labels\n", "    plt.title('Distribution of Spike Scores', fontsize=18, fontweight='bold')\n", "    plt.xlabel('Spike Score (Log Scale)', fontsize=12)\n", "    plt.ylabel('Frequency (Count)', fontsize=12)\n", "\n", "    # Add stats text\n", "    stats_text = f'Mean: {mean_s:,.2f}\\\\nMedian: {median_s:,.2f}\\\\nStd Dev: {std_s:,.2f}'\n", "    plt.text(0.95, 0.95, stats_text, transform=plt.gca().transAxes, fontsize=11,\n", "             verticalalignment='top', horizontalalignment='right', bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.6))\n", "\n", "    plt.grid(True, which='both', linestyle='--', linewidth=0.5)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully processed 1019 scores.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import math\n", "\n", "# --- Load and Process Data (same as before) ---\n", "def _calculate_spike_score(max_ppl_baseline, max_ppl_pressure):\n", "    if max_ppl_baseline > 0 and max_ppl_pressure > max_ppl_baseline:\n", "        absolute_diff = max_ppl_pressure - max_ppl_baseline\n", "        ratio = max_ppl_pressure / max_ppl_baseline\n", "        return absolute_diff * math.log(ratio + 1)\n", "    return 0.0\n", "\n", "file_path = 'ppl_results.jsonl'\n", "spike_scores = []\n", "try:\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            data = json.loads(line)\n", "            score = _calculate_spike_score(\n", "                data['baseline_result']['max_ppl'],\n", "                data['pressure_result']['max_ppl']\n", "            )\n", "            if score > 0:\n", "                spike_scores.append(score)\n", "    print(f\"Successfully processed {len(spike_scores)} scores.\")\n", "except FileNotFoundError:\n", "    print(f\"Error: The file '{file_path}' was not found.\")\n", "\n", "# --- Generate the CDF Plot with Vertical Lines ---\n", "if spike_scores:\n", "    plt.style.use('seaborn-v0_8-whitegrid')\n", "    plt.figure(figsize=(12, 7))\n", "\n", "    # Sort the data for the CDF\n", "    x_sorted = np.sort(spike_scores)\n", "    # Calculate the cumulative probability for the y-axis\n", "    y_cumulative = np.arange(1, len(x_sorted) + 1) / len(x_sorted)\n", "\n", "    # Create the plot\n", "    plt.plot(x_sorted, y_cumulative, marker='.', linestyle='none', markersize=2, label='CDF of Spike Scores')\n", "    plt.title('Cumulative Distribution of Spike Scores', fontsize=18, fontweight='bold')\n", "    plt.xlabel('Spike Score Threshold', fontsize=12)\n", "    plt.ylabel('Fraction of Data with Score ≤ Threshold', fontsize=12)\n", "    plt.grid(True, which=\"both\", ls=\"--\")\n", "\n", "    # Add vertical lines every 5 units\n", "    max_x = np.percentile(spike_scores, 95) # Get the x-limit for the lines\n", "    for i in range(0, int(max_x) + 5, 5):\n", "        plt.axvline(i, color='gray', linestyle=':', linewidth=0.75)\n", "\n", "    # Set a linear x-axis, but limit the view to see the crucial part of the curve\n", "    plt.xlim(0, max_x)\n", "\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "itas", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}