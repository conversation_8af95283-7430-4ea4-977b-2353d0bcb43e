#!/usr/bin/env python3
"""
Filter high perplexity examples from scheming dataset.

This script reads a JSONL dataset, calculates perplexity for each example using
a language model's chat template, and filters out examples with perplexity above
a specified threshold.
"""

import json
import torch
import argparse
import warnings
from pathlib import Path
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer

# Suppress transformers warnings for cleaner output
warnings.filterwarnings(
    "ignore", message="The attention mask and the pad token id were not set.*"
)


def calculate_perplexity(messages, model, tokenizer, device):
    """
    Calculate perplexity for a conversation using the model's chat template.

    Args:
        messages: List of message dicts with 'role' and 'content' keys
        model: The language model
        tokenizer: The tokenizer
        device: Device to run inference on

    Returns:
        float: Average perplexity of the conversation
    """
    # Apply chat template to format the conversation
    formatted_text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=False
    )

    # Tokenize the formatted text
    inputs = tokenizer(formatted_text, return_tensors="pt").to(device)

    with torch.no_grad():
        # Get model outputs with labels for loss calculation
        outputs = model(**inputs, labels=inputs["input_ids"])

    # Calculate per-token losses
    loss_fct = torch.nn.CrossEntropyLoss(reduction="none")
    logits = outputs.logits

    # Shift logits and labels for next-token prediction
    shift_logits = logits[..., :-1, :].contiguous()
    shift_labels = inputs["input_ids"][..., 1:].contiguous()

    # Calculate token-level losses
    token_losses = loss_fct(
        shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1)
    )

    # Convert losses to perplexities and calculate average
    token_perplexities = torch.exp(token_losses)
    avg_perplexity = token_perplexities.mean().item()

    return avg_perplexity


def process_batch(examples, model, tokenizer, device):
    """
    Process a batch of examples and calculate their perplexities.

    Args:
        examples: List of dataset examples
        model: The language model
        tokenizer: The tokenizer
        device: Device to run inference on

    Returns:
        List of perplexity values
    """
    perplexities = []

    for example in examples:
        # Construct messages using chat template format
        messages = [
            {"role": "system", "content": example["system_prompt"]},
            {"role": "user", "content": example["user_prompt"]},
            {"role": "assistant", "content": example["dishonest"]},
        ]

        try:
            ppl = calculate_perplexity(messages, model, tokenizer, device)
            perplexities.append(ppl)
        except Exception as e:
            print(f"Warning: Failed to calculate perplexity for example: {e}")
            perplexities.append(float("inf"))  # Mark as high perplexity

    return perplexities


def load_model_and_tokenizer(model_name, device):
    """Load the model and tokenizer."""
    print(f"Loading model: {model_name}")

    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16,
        device_map="auto" if torch.cuda.is_available() else None,
    )

    tokenizer = AutoTokenizer.from_pretrained(model_name)

    # Set pad token if not already set
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    print(f"✅ Model loaded on {device}")
    return model, tokenizer


def main():
    parser = argparse.ArgumentParser(
        description="Filter high perplexity examples from scheming dataset"
    )
    parser.add_argument(
        "--input_file",
        type=str,
        default="squad_scheming_dataset.jsonl",
        help="Input JSONL file path",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default="squad_scheming_dataset_filtered.jsonl",
        help="Output JSONL file path",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        default="meta-llama/Llama-3.1-8B-Instruct",
        help="Model name or path",
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=500.0,
        help="Perplexity threshold (keep examples with perplexity > threshold)",
    )
    parser.add_argument(
        "--batch_size",
        type=int,
        default=1,
        help="Batch size for processing (currently only supports 1)",
    )

    args = parser.parse_args()

    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Load model and tokenizer
    model, tokenizer = load_model_and_tokenizer(args.model_name, device)

    # Read input file
    input_path = Path(args.input_file)
    if not input_path.exists():
        raise FileNotFoundError(f"Input file not found: {args.input_file}")

    print(f"Reading examples from: {args.input_file}")
    examples = []
    with open(input_path, "r", encoding="utf-8") as f:
        for line in f:
            examples.append(json.loads(line.strip()))

    print(f"Loaded {len(examples)} examples")

    # Process examples and calculate perplexities
    print("Calculating perplexities...")
    filtered_examples = []
    total_examples = len(examples)

    with tqdm(total=total_examples, desc="Processing examples") as pbar:
        for i in range(0, total_examples, args.batch_size):
            batch = examples[i : i + args.batch_size]
            perplexities = process_batch(batch, model, tokenizer, device)

            # Filter based on threshold
            for example, ppl in zip(batch, perplexities):
                if ppl > args.threshold:
                    filtered_examples.append(example)

            pbar.update(len(batch))

    # Save filtered results
    output_path = Path(args.output_file)
    print(f"Saving filtered examples to: {args.output_file}")

    with open(output_path, "w", encoding="utf-8") as f:
        for example in filtered_examples:
            f.write(json.dumps(example, ensure_ascii=False) + "\n")

    # Print summary statistics
    kept_count = len(filtered_examples)
    filtered_count = total_examples - kept_count
    percentage_kept = (kept_count / total_examples) * 100 if total_examples > 0 else 0

    print("\n" + "=" * 50)
    print("FILTERING SUMMARY")
    print("=" * 50)
    print(f"Total examples processed: {total_examples}")
    print(f"Examples kept (perplexity > {args.threshold}): {kept_count}")
    print(f"Examples filtered out: {filtered_count}")
    print(f"Percentage kept: {percentage_kept:.2f}%")
    print(f"Output saved to: {args.output_file}")


if __name__ == "__main__":
    main()
