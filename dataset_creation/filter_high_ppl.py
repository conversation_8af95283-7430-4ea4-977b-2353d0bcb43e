#!/usr/bin/env python3
"""
Two-stage perplexity filtering pipeline for scheming dataset.

Stage 1: Calculate perplexity for both honest and dishonest responses
Stage 2: Filter examples based on higher perplexity threshold

Usage:
    python filter_high_ppl.py calculate_ppl --input_file dataset.jsonl --output_file dataset_with_ppl.jsonl
    python filter_high_ppl.py filter_by_ppl --input_file dataset_with_ppl.jsonl --output_file filtered.jsonl --threshold 500
"""

import json
import torch
import warnings
import numpy as np
from pathlib import Path
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer
import fire

# Suppress transformers warnings for cleaner output
warnings.filterwarnings(
    "ignore", message="The attention mask and the pad token id were not set.*"
)


def calculate_perplexity(messages, model, tokenizer, device):
    """
    Calculate perplexity for a conversation using the model's chat template.

    Args:
        messages: List of message dicts with 'role' and 'content' keys
        model: The language model
        tokenizer: The tokenizer
        device: Device to run inference on

    Returns:
        float: Average perplexity of the conversation
    """
    # Apply chat template to format the conversation
    formatted_text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=False
    )

    # Tokenize the formatted text
    inputs = tokenizer(formatted_text, return_tensors="pt").to(device)

    with torch.no_grad():
        # Get model outputs with labels for loss calculation
        outputs = model(**inputs, labels=inputs["input_ids"])

    # Calculate per-token losses
    loss_fct = torch.nn.CrossEntropyLoss(reduction="none")
    logits = outputs.logits

    # Shift logits and labels for next-token prediction
    shift_logits = logits[..., :-1, :].contiguous()
    shift_labels = inputs["input_ids"][..., 1:].contiguous()

    # Calculate token-level losses
    token_losses = loss_fct(
        shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1)
    )

    # Convert losses to perplexities and calculate average
    token_perplexities = torch.exp(token_losses)
    avg_perplexity = token_perplexities.mean().item()

    return avg_perplexity


def calculate_dual_perplexity(example, model, tokenizer, device):
    """
    Calculate perplexity for both honest and dishonest responses.

    Args:
        example: Dataset example with system_prompt, user_prompt, honest, dishonest fields
        model: The language model
        tokenizer: The tokenizer
        device: Device to run inference on

    Returns:
        dict: Contains honest_ppl, dishonest_ppl, and higher_ppl
    """
    results = {}

    # Calculate perplexity for honest response
    honest_messages = [
        {"role": "system", "content": example["system_prompt"]},
        {"role": "user", "content": example["user_prompt"]},
        {"role": "assistant", "content": example["honest"]},
    ]

    # Calculate perplexity for dishonest response
    dishonest_messages = [
        {"role": "system", "content": example["system_prompt"]},
        {"role": "user", "content": example["user_prompt"]},
        {"role": "assistant", "content": example["dishonest"]},
    ]

    try:
        honest_ppl = calculate_perplexity(honest_messages, model, tokenizer, device)
        results["honest_ppl"] = honest_ppl
    except Exception as e:
        print(f"Warning: Failed to calculate honest perplexity: {e}")
        results["honest_ppl"] = float("inf")

    try:
        dishonest_ppl = calculate_perplexity(
            dishonest_messages, model, tokenizer, device
        )
        results["dishonest_ppl"] = dishonest_ppl
    except Exception as e:
        print(f"Warning: Failed to calculate dishonest perplexity: {e}")
        results["dishonest_ppl"] = float("inf")

    # Calculate higher perplexity
    results["higher_ppl"] = max(results["honest_ppl"], results["dishonest_ppl"])

    return results


def load_model_and_tokenizer(model_name, device):
    """Load the model and tokenizer."""
    print(f"Loading model: {model_name}")

    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16,
        device_map="auto" if torch.cuda.is_available() else None,
    )

    tokenizer = AutoTokenizer.from_pretrained(model_name)

    # Set pad token if not already set
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    print(f"✅ Model loaded on {device}")
    return model, tokenizer


def print_perplexity_statistics(perplexities, metric_name):
    """Print distribution statistics for perplexity values."""
    if not perplexities:
        print(f"No valid {metric_name} values found.")
        return

    # Filter out infinite values for statistics
    finite_ppls = [ppl for ppl in perplexities if not np.isinf(ppl)]

    if not finite_ppls:
        print(f"No finite {metric_name} values found.")
        return

    print(f"\n--- {metric_name} Statistics ---")
    print(f"Count: {len(finite_ppls)}")
    print(f"Min: {np.min(finite_ppls):.2f}")
    print(f"Max: {np.max(finite_ppls):.2f}")
    print(f"Mean: {np.mean(finite_ppls):.2f}")
    print(f"Median: {np.median(finite_ppls):.2f}")
    print(f"75th percentile: {np.percentile(finite_ppls, 75):.2f}")
    print(f"90th percentile: {np.percentile(finite_ppls, 90):.2f}")
    print(f"95th percentile: {np.percentile(finite_ppls, 95):.2f}")
    print(f"99th percentile: {np.percentile(finite_ppls, 99):.2f}")


class PerplexityPipeline:
    """Two-stage perplexity filtering pipeline."""

    def calculate_ppl(
        self,
        input_file: str = "squad_scheming_dataset.jsonl",
        output_file: str = "squad_scheming_dataset_with_ppl.jsonl",
        model_name: str = "meta-llama/Llama-3.1-8B-Instruct",
    ):
        """
        Stage 1: Calculate perplexity for both honest and dishonest responses.

        Args:
            input_file: Input JSONL file path
            output_file: Output JSONL file path with perplexity scores
            model_name: HuggingFace model name or path
        """
        print("=== Stage 1: Calculating Perplexities ===")

        # Setup device
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {device}")

        # Load model and tokenizer
        model, tokenizer = load_model_and_tokenizer(model_name, device)

        # Read input file
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file}")

        print(f"Reading examples from: {input_file}")
        examples = []
        with open(input_path, "r", encoding="utf-8") as f:
            for line in f:
                examples.append(json.loads(line.strip()))

        print(f"Loaded {len(examples)} examples")

        # Process examples and calculate dual perplexities
        print("Calculating perplexities for honest and dishonest responses...")
        enriched_examples = []
        honest_ppls = []
        dishonest_ppls = []
        higher_ppls = []

        with tqdm(total=len(examples), desc="Processing examples") as pbar:
            for example in examples:
                ppl_results = calculate_dual_perplexity(
                    example, model, tokenizer, device
                )

                # Create enriched example with perplexity scores
                enriched_example = example.copy()
                enriched_example.update(ppl_results)
                enriched_examples.append(enriched_example)

                # Collect statistics
                honest_ppls.append(ppl_results["honest_ppl"])
                dishonest_ppls.append(ppl_results["dishonest_ppl"])
                higher_ppls.append(ppl_results["higher_ppl"])

                pbar.update(1)

        # Save enriched results
        output_path = Path(output_file)
        print(f"Saving enriched examples to: {output_file}")

        with open(output_path, "w", encoding="utf-8") as f:
            for example in enriched_examples:
                f.write(json.dumps(example, ensure_ascii=False) + "\n")

        # Print distribution statistics
        print_perplexity_statistics(honest_ppls, "Honest Perplexity")
        print_perplexity_statistics(dishonest_ppls, "Dishonest Perplexity")
        print_perplexity_statistics(higher_ppls, "Higher Perplexity")

        print(f"\n✅ Stage 1 complete. Enriched dataset saved to: {output_file}")

    def filter_by_ppl(
        self,
        input_file: str = "squad_scheming_dataset_with_ppl.jsonl",
        output_file: str = "squad_scheming_dataset_filtered.jsonl",
        threshold: float = 500.0,
    ):
        """
        Stage 2: Filter examples based on higher_ppl threshold.

        Args:
            input_file: Input JSONL file with perplexity scores
            output_file: Output JSONL file with filtered examples
            threshold: Perplexity threshold (keep examples with higher_ppl > threshold)
        """
        print("=== Stage 2: Filtering by Perplexity ===")

        # Read input file with perplexity scores
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file}")

        print(f"Reading examples with perplexity scores from: {input_file}")
        examples = []
        with open(input_path, "r", encoding="utf-8") as f:
            for line in f:
                examples.append(json.loads(line.strip()))

        print(f"Loaded {len(examples)} examples")

        # Validate that examples have perplexity scores
        if examples and "higher_ppl" not in examples[0]:
            raise ValueError(
                f"Input file {input_file} does not contain perplexity scores. "
                "Please run calculate_ppl first."
            )

        # Filter examples based on higher_ppl threshold
        print(f"Filtering examples with higher_ppl > {threshold}...")
        filtered_examples = []

        with tqdm(total=len(examples), desc="Filtering examples") as pbar:
            for example in examples:
                if example.get("higher_ppl", 0) > threshold:
                    # Remove perplexity fields from output (optional)
                    filtered_example = {
                        k: v
                        for k, v in example.items()
                        if k not in ["honest_ppl", "dishonest_ppl", "higher_ppl"]
                    }
                    filtered_examples.append(filtered_example)
                pbar.update(1)

        # Save filtered results
        output_path = Path(output_file)
        print(f"Saving filtered examples to: {output_file}")

        with open(output_path, "w", encoding="utf-8") as f:
            for example in filtered_examples:
                f.write(json.dumps(example, ensure_ascii=False) + "\n")

        # Print summary statistics
        total_examples = len(examples)
        kept_count = len(filtered_examples)
        filtered_count = total_examples - kept_count
        percentage_kept = (
            (kept_count / total_examples) * 100 if total_examples > 0 else 0
        )

        print("\n" + "=" * 50)
        print("FILTERING SUMMARY")
        print("=" * 50)
        print(f"Total examples processed: {total_examples}")
        print(f"Examples kept (higher_ppl > {threshold}): {kept_count}")
        print(f"Examples filtered out: {filtered_count}")
        print(f"Percentage kept: {percentage_kept:.2f}%")
        print(f"Output saved to: {output_file}")

        print(f"\n✅ Stage 2 complete. Filtered dataset saved to: {output_file}")


if __name__ == "__main__":
    fire.Fire(PerplexityPipeline)
