import json
import numpy as np
import matplotlib.pyplot as plt
import math

# Define the spike score calculation function
def _calculate_spike_score(max_ppl_baseline, max_ppl_pressure):
    """Helper to calculate the spike score."""
    if max_ppl_baseline > 0 and max_ppl_pressure > max_ppl_baseline:
        absolute_diff = max_ppl_pressure - max_ppl_baseline
        ratio = max_ppl_pressure / max_ppl_baseline
        return absolute_diff * math.log(ratio + 1)
    return 0.0

# Load the data from the JSONL file
def load_data(file_path):
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line))
    return data

# --- Data Extraction ---
file_path = 'ppl_results.jsonl'
try:
    ppl_data = load_data(file_path)

    # Extract PPLs and calculate spike scores
    baseline_avg_ppls = [item['baseline_result']['avg_ppl'] for item in ppl_data]
    pressure_avg_ppls = [item['pressure_result']['avg_ppl'] for item in ppl_data]
    spike_scores = [
        _calculate_spike_score(
            item['baseline_result']['max_ppl'],
            item['pressure_result']['max_ppl']
        ) for item in ppl_data
    ]
    # Filter out zero scores for a more meaningful distribution plot
    spike_scores = [s for s in spike_scores if s > 0]

    print(f"Successfully loaded and processed {len(ppl_data)} records.")
except FileNotFoundError:
    print(f"Error: The file '{file_path}' was not found. Please run the `generate_ppl` command first.")
    baseline_avg_ppls, pressure_avg_ppls, spike_scores = [], [], []

plt.style.use('seaborn-v0_8-whitegrid')
fig, axes = plt.subplots(1, 2, figsize=(16, 6), sharey=True)
fig.suptitle('Average Perplexity (PPL) Distribution', fontsize=18, fontweight='bold')

# --- Plot 1: Baseline PPL Distribution ---
if baseline_avg_ppls:
    # Calculate stats
    mean_bl = np.mean(baseline_avg_ppls)
    median_bl = np.median(baseline_avg_ppls)
    std_bl = np.std(baseline_avg_ppls)

    # Define bins
    max_val = math.ceil(max(baseline_avg_ppls))
    bins_bl = np.arange(0, max_val + 2, 1.0)

    # Plot histogram
    axes[0].hist(baseline_avg_ppls, bins=bins_bl, color='#4c72b0', alpha=0.8, edgecolor='black')
    axes[0].set_title('Baseline Scenario Responses', fontsize=14)
    axes[0].set_xlabel('Average Perplexity (PPL)', fontsize=12)
    axes[0].set_ylabel('Frequency (Count)', fontsize=12)
    axes[0].set_xlim(0, max(20, max_val)) # Set a reasonable x-limit

    # Add stats text
    stats_text = f'Mean: {mean_bl:.2f}\nMedian: {median_bl:.2f}\nStd Dev: {std_bl:.2f}'
    axes[0].text(0.95, 0.95, stats_text, transform=axes[0].transAxes, fontsize=11,
                 verticalalignment='top', horizontalalignment='right', bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.6))

# --- Plot 2: Pressure PPL Distribution ---
if pressure_avg_ppls:
    # Calculate stats
    mean_pr = np.mean(pressure_avg_ppls)
    median_pr = np.median(pressure_avg_ppls)
    std_pr = np.std(pressure_avg_ppls)

    # Define bins
    max_val = math.ceil(max(pressure_avg_ppls))
    bins_pr = np.arange(0, max_val + 2, 1.0)

    # Plot histogram
    axes[1].hist(pressure_avg_ppls, bins=bins_pr, color='#dd8452', alpha=0.8, edgecolor='black')
    axes[1].set_title('Pressure Scenario Responses', fontsize=14)
    axes[1].set_xlabel('Average Perplexity (PPL)', fontsize=12)
    axes[1].set_xlim(0, max(20, max_val))

    # Add stats text
    stats_text = f'Mean: {mean_pr:.2f}\nMedian: {median_pr:.2f}\nStd Dev: {std_pr:.2f}'
    axes[1].text(0.95, 0.95, stats_text, transform=axes[1].transAxes, fontsize=11,
                 verticalalignment='top', horizontalalignment='right', bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.6))

plt.tight_layout(rect=[0, 0, 1, 0.96])
plt.show()

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from matplotlib.ticker import ScalarFormatter # Import the formatter

if spike_scores:
    plt.figure(figsize=(12, 7))

    # Calculate stats
    mean_s = np.mean(spike_scores)
    median_s = np.median(spike_scores)
    std_s = np.std(spike_scores)

    # Create logarithmically spaced bins for the histogram
    min_val = np.log10(min(spike_scores))
    max_val = np.log10(max(spike_scores))
    log_bins = np.logspace(min_val, max_val, 50)

    # Create histogram with the new log bins
    plt.hist(spike_scores, bins=log_bins, color='#55a868', alpha=0.8, edgecolor='black')
    plt.gca().set_xscale('log')

    # --- FIX: Format the x-axis to show regular numbers ---
    ax = plt.gca()
    formatter = ScalarFormatter()
    formatter.set_scientific(False)
    ax.xaxis.set_major_formatter(formatter)

    # Titles and labels
    plt.title('Distribution of Spike Scores', fontsize=18, fontweight='bold')
    plt.xlabel('Spike Score (Log Scale)', fontsize=12)
    plt.ylabel('Frequency (Count)', fontsize=12)

    # Add stats text
    stats_text = f'Mean: {mean_s:,.2f}\\nMedian: {median_s:,.2f}\\nStd Dev: {std_s:,.2f}'
    plt.text(0.95, 0.95, stats_text, transform=plt.gca().transAxes, fontsize=11,
             verticalalignment='top', horizontalalignment='right', bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.6))

    plt.grid(True, which='both', linestyle='--', linewidth=0.5)
    plt.tight_layout()
    plt.show()

import json
import numpy as np
import matplotlib.pyplot as plt
import math

# --- Load and Process Data (same as before) ---
def _calculate_spike_score(max_ppl_baseline, max_ppl_pressure):
    if max_ppl_baseline > 0 and max_ppl_pressure > max_ppl_baseline:
        absolute_diff = max_ppl_pressure - max_ppl_baseline
        ratio = max_ppl_pressure / max_ppl_baseline
        return absolute_diff * math.log(ratio + 1)
    return 0.0

file_path = 'ppl_results.jsonl'
spike_scores = []
try:
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data = json.loads(line)
            score = _calculate_spike_score(
                data['baseline_result']['max_ppl'],
                data['pressure_result']['max_ppl']
            )
            if score > 0:
                spike_scores.append(score)
    print(f"Successfully processed {len(spike_scores)} scores.")
except FileNotFoundError:
    print(f"Error: The file '{file_path}' was not found.")

# --- Generate the CDF Plot with Vertical Lines ---
if spike_scores:
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.figure(figsize=(12, 7))

    # Sort the data for the CDF
    x_sorted = np.sort(spike_scores)
    # Calculate the cumulative probability for the y-axis
    y_cumulative = np.arange(1, len(x_sorted) + 1) / len(x_sorted)

    # Create the plot
    plt.plot(x_sorted, y_cumulative, marker='.', linestyle='none', markersize=2, label='CDF of Spike Scores')
    plt.title('Cumulative Distribution of Spike Scores', fontsize=18, fontweight='bold')
    plt.xlabel('Spike Score Threshold', fontsize=12)
    plt.ylabel('Fraction of Data with Score ≤ Threshold', fontsize=12)
    plt.grid(True, which="both", ls="--")

    # Add vertical lines every 5 units
    max_x = np.percentile(spike_scores, 95) # Get the x-limit for the lines
    for i in range(0, int(max_x) + 5, 5):
        plt.axvline(i, color='gray', linestyle=':', linewidth=0.75)

    # Set a linear x-axis, but limit the view to see the crucial part of the curve
    plt.xlim(0, max_x)

    plt.legend()
    plt.tight_layout()
    plt.show()